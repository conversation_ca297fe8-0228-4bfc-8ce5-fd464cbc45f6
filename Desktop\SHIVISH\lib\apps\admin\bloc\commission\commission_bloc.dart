import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/shared/models/commission.dart';
import 'package:shivish/shared/services/commission/commission_service.dart';

part 'commission_bloc.freezed.dart';

@freezed
class CommissionEvent with _$CommissionEvent {
  const factory CommissionEvent.loadCommissions({
    int? limit,
    DocumentSnapshot? startAfter,
    Map<String, dynamic>? filters,
  }) = LoadCommissions;

  const factory CommissionEvent.createCommission(Commission commission) =
      CreateCommission;

  const factory CommissionEvent.updateCommission(Commission commission) =
      UpdateCommission;

  const factory CommissionEvent.deleteCommission(String id) = DeleteCommission;

  const factory CommissionEvent.calculateCommission(
    String categoryId,
    double amount,
  ) = CalculateCommission;
}

@freezed
class CommissionState with _$CommissionState {
  const factory CommissionState.initial() = CommissionInitial;
  const factory CommissionState.loading() = CommissionLoading;
  const factory CommissionState.loaded(List<Commission> commissions) =
      CommissionLoaded;
  const factory CommissionState.calculated(double amount) =
      CommissionCalculated;
  const factory CommissionState.error(String message) = CommissionError;
}

@injectable
class CommissionBloc extends Bloc<CommissionEvent, CommissionState> {
  final CommissionService _commissionService;

  CommissionBloc(this._commissionService)
      : super(const CommissionState.initial()) {
    on<LoadCommissions>(_onLoadCommissions);
    on<CreateCommission>(_onCreateCommission);
    on<UpdateCommission>(_onUpdateCommission);
    on<DeleteCommission>(_onDeleteCommission);
    on<CalculateCommission>(_onCalculateCommission);
  }

  Future<void> _onLoadCommissions(
    LoadCommissions event,
    Emitter<CommissionState> emit,
  ) async {
    try {
      emit(const CommissionState.loading());
      final commissions = await _commissionService.getCommissions(
        limit: event.limit,
        startAfter: event.startAfter,
        filters: event.filters,
      );
      emit(CommissionState.loaded(commissions));
    } catch (e) {
      emit(CommissionState.error(e.toString()));
    }
  }

  Future<void> _onCreateCommission(
    CreateCommission event,
    Emitter<CommissionState> emit,
  ) async {
    try {
      emit(const CommissionState.loading());
      final commission =
          await _commissionService.createCommission(event.commission);
      final currentState = state;
      if (currentState is CommissionLoaded) {
        emit(CommissionState.loaded([...currentState.commissions, commission]));
      }
    } catch (e) {
      emit(CommissionState.error(e.toString()));
    }
  }

  Future<void> _onUpdateCommission(
    UpdateCommission event,
    Emitter<CommissionState> emit,
  ) async {
    try {
      emit(const CommissionState.loading());
      final commission =
          await _commissionService.updateCommission(event.commission);
      final currentState = state;
      if (currentState is CommissionLoaded) {
        final updatedCommissions = currentState.commissions.map((c) {
          return c.id == commission.id ? commission : c;
        }).toList();
        emit(CommissionState.loaded(updatedCommissions));
      }
    } catch (e) {
      emit(CommissionState.error(e.toString()));
    }
  }

  Future<void> _onDeleteCommission(
    DeleteCommission event,
    Emitter<CommissionState> emit,
  ) async {
    try {
      emit(const CommissionState.loading());
      await _commissionService.deleteCommission(event.id);
      final currentState = state;
      if (currentState is CommissionLoaded) {
        final updatedCommissions =
            currentState.commissions.where((c) => c.id != event.id).toList();
        emit(CommissionState.loaded(updatedCommissions));
      }
    } catch (e) {
      emit(CommissionState.error(e.toString()));
    }
  }

  Future<void> _onCalculateCommission(
    CalculateCommission event,
    Emitter<CommissionState> emit,
  ) async {
    try {
      emit(const CommissionState.loading());
      final commission = await _commissionService.calculateCommission(
        event.categoryId,
        event.amount,
      );
      emit(CommissionState.calculated(commission));
    } catch (e) {
      emit(CommissionState.error(e.toString()));
    }
  }
}
