import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/alarm/alarm_model.dart';
import '../repositories/alarm_repository.dart';
import '../providers/alarm_provider.dart' as alarm_providers;
import '../providers/auth_provider.dart';
import 'alarm_state.dart' as alarm_state;
import 'package:supabase_flutter/supabase_flutter.dart';

part 'alarm_state_notifier.g.dart';

@riverpod
class AlarmStateNotifier extends _$AlarmStateNotifier {
  late final AlarmRepository _repository;

  @override
  alarm_state.AlarmState build() {
    _repository = ref.watch(alarm_providers.alarmRepositoryProvider);
    _loadAlarms();
    return const alarm_state.AlarmState();
  }

  Future<void> _loadAlarms() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final authState = ref.watch(authProvider);
      final userId = authState?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }
      final alarms = await _repository.getAlarms(userId);
      state = state.copyWith(
        alarms: alarms,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> saveAlarm(AlarmModel alarm) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final authState = ref.watch(authProvider);
      final userId = authState?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }
      await _repository.saveAlarm(userId, alarm);
      await _loadAlarms();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> deleteAlarm(AlarmModel alarm) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final authState = ref.watch(authProvider);
      final userId = authState?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }
      await _repository.deleteAlarm(userId, alarm);
      await _loadAlarms();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> updateAlarm(AlarmModel alarm) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final authState = ref.watch(authProvider);
      final userId = authState?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }
      await _repository.updateAlarm(userId, alarm);
      await _loadAlarms();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> snoozeAlarm(AlarmModel alarm) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final authState = ref.watch(authProvider);
      final userId = authState?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }
      await _repository.snoozeAlarm(userId, alarm);
      await _loadAlarms();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}
