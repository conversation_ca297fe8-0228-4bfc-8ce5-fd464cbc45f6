import 'package:injectable/injectable.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/models/user/user_model.dart';
import '../models/login_request.dart';
import '../models/register_request.dart';
import '../../../services/auth/supabase_auth_service.dart';

@singleton
class AuthRepository {
  final SupabaseAuthService _authService = SupabaseAuthService.instance;
  final SupabaseClient _supabase = Supabase.instance.client;

  AuthRepository() {
    debugPrint('Supabase AuthRepository initialized');
  }

  /// Get auth state changes stream
  Stream<UserModel?> get authStateChanges {
    return _supabase.auth.onAuthStateChange.asyncMap((state) async {
      if (state.session?.user == null) return null;

      // Get user profile from database
      final userProfile = await _getUserProfile(state.session!.user.id);
      return userProfile ?? _createUserModelFromSupabaseUser(state.session!.user);
    });
  }

  /// Get current user async
  Future<UserModel?> getCurrentUser() async {
    final user = _authService.currentUser;
    if (user == null) return null;

    final userProfile = await _getUserProfile(user.id);
    return userProfile ?? _createUserModelFromSupabaseUser(user);
  }

  /// Get current user sync
  UserModel? get currentUser {
    return _authService.currentUserModel;
  }

  /// Sign in with email and password
  Future<UserModel> signInWithEmailAndPassword(LoginRequest request) async {
    try {
      debugPrint('AuthRepository: Attempting to sign in with email: ${request.email}');

      return await _authService.signInWithEmailAndPassword(request);
    } catch (e) {
      debugPrint('AuthRepository: Sign in error: $e');
      rethrow;
    }
  }

  Future<UserModel> register(RegisterRequest request) async {
    try {
      final result = await _auth.createUserWithEmailAndPassword(
        email: request.email,
        password: request.password,
      );

      final user = result.user!;
      await user.updateDisplayName(request.displayName);

      // Set approval status based on role
      bool isApproved = true; // Default for most roles

      // Executors need admin approval
      if (request.role == UserRole.executor) {
        isApproved = false;

        // Create an executor request document
        await _firestore.collection('executorRequests').doc(user.uid).set({
          'uid': user.uid,
          'email': request.email,
          'displayName': request.displayName,
          'status': 'pending',
          'requestedAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        debugPrint('Created executor request document for approval');
      }

      final userModel = UserModel.fromFirebaseUser(user).copyWith(
        displayName: request.displayName,
        role: request.role,
      );

      // Add user to Firestore with approval status
      await _firestore
          .collection('users')
          .doc(user.uid)
          .set({
            ...userModel.toJson(),
            'isApproved': isApproved,
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });

      // If this is an executor and they need approval, sign them out
      if (request.role == UserRole.executor && !isApproved) {
        await _auth.signOut();
      }

      return userModel;
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<UserModel> signInWithGoogle() async {
    try {
      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) throw 'Google sign in aborted';

      final googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final result = await _auth.signInWithCredential(credential);
      return UserModel.fromFirebaseUser(result.user!);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<UserModel> signInWithApple() async {
    try {
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      final result = await _auth.signInWithCredential(oauthCredential);
      return UserModel.fromFirebaseUser(result.user!);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<void> signOut({bool clearAllCredentials = true}) async {
    try {
      debugPrint(
          'Signing out user with clearAllCredentials=$clearAllCredentials');

      if (clearAllCredentials) {
        // Full sign out from all providers
        debugPrint('Performing full sign out from all providers');
        await Future.wait([
          _auth.signOut(),
          _googleSignIn.signOut(),
        ]);
      } else {
        // Only sign out from Firebase Auth but keep credentials for auto-login
        debugPrint('Performing partial sign out (Firebase Auth only)');
        await _auth.signOut();
      }

      debugPrint('Sign out completed successfully');
    } catch (e) {
      debugPrint('Error during sign out: $e');
      throw _handleAuthError(e);
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<UserModel> updateProfile({
    String? displayName,
    String? photoUrl,
    String? phoneNumber,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('No authenticated user');

      if (displayName != null) await user.updateDisplayName(displayName);
      if (photoUrl != null) await user.updatePhotoURL(photoUrl);

      final userModel = UserModel.fromFirebaseUser(user);
      await _firestore.collection('users').doc(user.uid).update({
        if (displayName != null) 'displayName': displayName,
        if (photoUrl != null) 'photoUrl': photoUrl,
        if (phoneNumber != null) 'phoneNumber': phoneNumber,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return userModel;
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<void> deleteAccount() async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('No authenticated user');

      await _firestore.collection('users').doc(user.uid).delete();
      await user.delete();
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<void> verifyPhone(String phoneNumber) async {
    try {
      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (_) {},
        verificationFailed: (e) => throw _handleAuthError(e),
        codeSent: (_, __) {},
        codeAutoRetrievalTimeout: (_) {},
      );
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<UserModel> verifyPhoneCode(String code) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('No authenticated user');

      return UserModel.fromFirebaseUser(user);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<bool> isEmailVerified() async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('No authenticated user');

      await user.reload();
      return user.emailVerified;
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Exception _handleAuthError(dynamic error) {
    debugPrint('AuthRepository: Handling auth error: $error');

    if (error is FirebaseAuthException) {
      debugPrint('AuthRepository: FirebaseAuthException code: ${error.code}');
      debugPrint('AuthRepository: FirebaseAuthException message: ${error.message}');

      switch (error.code) {
        case 'user-not-found':
          return Exception('No user found with this email');
        case 'wrong-password':
          return Exception('Wrong password');
        case 'email-already-in-use':
          return Exception('Email is already in use');
        case 'invalid-email':
          return Exception('Invalid email address');
        case 'weak-password':
          return Exception('Password is too weak');
        case 'operation-not-allowed':
          return Exception('Operation not allowed');
        case 'user-disabled':
          return Exception('User has been disabled');
        case 'network-request-failed':
          return Exception('Network error. Please check your internet connection and try again.');
        case 'too-many-requests':
          return Exception('Too many failed login attempts. Please try again later.');
        case 'invalid-credential':
          return Exception('Invalid login credentials. Please check your email and password.');
        default:
          return Exception(error.message ?? 'Authentication error occurred');
      }
    } else if (error is Exception) {
      // Handle other types of exceptions
      debugPrint('AuthRepository: Non-Firebase exception: $error');
      return error;
    }

    // For any other type of error
    debugPrint('AuthRepository: Unknown error type: $error');
    return Exception('An unexpected error occurred. Please try again.');
  }
}
