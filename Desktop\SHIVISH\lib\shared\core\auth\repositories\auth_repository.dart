import 'package:injectable/injectable.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/models/user/user_model.dart';
import '../models/login_request.dart';
import '../models/register_request.dart';
import '../../../services/auth/supabase_auth_service.dart';

@singleton
class AuthRepository {
  final SupabaseAuthService _authService = SupabaseAuthService.instance;
  final SupabaseClient _supabase = Supabase.instance.client;

  AuthRepository() {
    debugPrint('Supabase AuthRepository initialized');
  }

  /// Get auth state changes stream
  Stream<UserModel?> get authStateChanges {
    return _supabase.auth.onAuthStateChange.asyncMap((state) async {
      if (state.session?.user == null) return null;

      // Get user profile from database
      final userProfile = await _getUserProfile(state.session!.user.id);
      return userProfile ?? _createUserModelFromSupabaseUser(state.session!.user);
    });
  }

  /// Get current user async
  Future<UserModel?> getCurrentUser() async {
    final user = _authService.currentUser;
    if (user == null) return null;

    final userProfile = await _getUserProfile(user.id);
    return userProfile ?? _createUserModelFromSupabaseUser(user);
  }

  /// Get current user sync
  UserModel? get currentUser {
    return _authService.currentUserModel;
  }

  /// Sign in with email and password
  Future<UserModel> signInWithEmailAndPassword(LoginRequest request) async {
    try {
      debugPrint('AuthRepository: Attempting to sign in with email: ${request.email}');

      return await _authService.signInWithEmailAndPassword(request);
    } catch (e) {
      debugPrint('AuthRepository: Sign in error: $e');
      rethrow;
    }
  }

  Future<UserModel> register(RegisterRequest request) async {
    try {
      debugPrint('AuthRepository: Attempting to register user with email: ${request.email}');

      return await _authService.register(request);
    } catch (e) {
      debugPrint('AuthRepository: Registration error: $e');
      rethrow;
    }
  }

  Future<UserModel> signInWithGoogle() async {
    try {
      debugPrint('AuthRepository: Attempting Google sign in');
      return await _authService.signInWithGoogle();
    } catch (e) {
      debugPrint('AuthRepository: Google sign in error: $e');
      rethrow;
    }
  }

  Future<UserModel> signInWithApple() async {
    try {
      debugPrint('AuthRepository: Attempting Apple sign in');
      return await _authService.signInWithApple();
    } catch (e) {
      debugPrint('AuthRepository: Apple sign in error: $e');
      rethrow;
    }
  }

  Future<void> signOut({bool clearAllCredentials = true}) async {
    try {
      debugPrint('AuthRepository: Signing out user');
      await _authService.signOut();
      debugPrint('Sign out completed successfully');
    } catch (e) {
      debugPrint('Error during sign out: $e');
      rethrow;
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      debugPrint('AuthRepository: Sending password reset email to: $email');
      await _authService.resetPassword(email);
    } catch (e) {
      debugPrint('AuthRepository: Reset password error: $e');
      rethrow;
    }
  }

  Future<UserModel> updateProfile({
    String? displayName,
    String? photoUrl,
    String? phoneNumber,
  }) async {
    try {
      debugPrint('AuthRepository: Updating user profile');
      return await _authService.updateProfile(
        displayName: displayName,
        photoUrl: photoUrl,
        phoneNumber: phoneNumber,
      );
    } catch (e) {
      debugPrint('AuthRepository: Update profile error: $e');
      rethrow;
    }
  }

  Future<void> deleteAccount() async {
    try {
      debugPrint('AuthRepository: Deleting user account');
      await _authService.deleteAccount();
    } catch (e) {
      debugPrint('AuthRepository: Delete account error: $e');
      rethrow;
    }
  }

  Future<void> verifyPhone(String phoneNumber) async {
    try {
      debugPrint('AuthRepository: Phone verification not yet implemented for Supabase');
      throw Exception('Phone verification not yet implemented');
    } catch (e) {
      debugPrint('AuthRepository: Phone verification error: $e');
      rethrow;
    }
  }

  Future<UserModel> verifyPhoneCode(String code) async {
    try {
      debugPrint('AuthRepository: Phone code verification not yet implemented for Supabase');
      throw Exception('Phone code verification not yet implemented');
    } catch (e) {
      debugPrint('AuthRepository: Phone code verification error: $e');
      rethrow;
    }
  }

  Future<bool> isEmailVerified() async {
    try {
      final user = _authService.currentUser;
      if (user == null) throw Exception('No authenticated user');

      // In Supabase, email verification is handled differently
      return user.emailConfirmedAt != null;
    } catch (e) {
      debugPrint('AuthRepository: Email verification check error: $e');
      rethrow;
    }
  }

  /// Get user profile from Supabase database
  Future<UserModel?> _getUserProfile(String userId) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select()
          .eq('id', userId)
          .maybeSingle();

      if (response == null) return null;

      return UserModel.fromJson(response);
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      return null;
    }
  }

  /// Create UserModel from Supabase User
  UserModel _createUserModelFromSupabaseUser(User user) {
    return UserModel(
      id: user.id,
      email: user.email ?? '',
      displayName: user.userMetadata?['display_name'] ?? '',
      photoUrl: user.userMetadata?['avatar_url'],
      phoneNumber: user.phone,
      role: UserRole.buyer, // Default role
      createdAt: DateTime.parse(user.createdAt),
      updatedAt: DateTime.now(),
    );
  }
}
