import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:equatable/equatable.dart';
import '../../../../shared/models/user/user_model.dart';

// Events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class SignInEvent extends AuthEvent {
  final String email;
  final String password;

  const SignInEvent({required this.email, required this.password});

  @override
  List<Object?> get props => [email, password];
}

class SignOutEvent extends AuthEvent {}

class CheckAuthEvent extends AuthEvent {}

class RegisterAdminEvent extends AuthEvent {
  final String email;
  final String password;
  final String displayName;
  final String reason;

  const RegisterAdminEvent({
    required this.email,
    required this.password,
    required this.displayName,
    required this.reason,
  });

  @override
  List<Object?> get props => [email, password, displayName, reason];
}

class ApproveAdminEvent extends AuthEvent {
  final String requestId;

  const ApproveAdminEvent({required this.requestId});

  @override
  List<Object?> get props => [requestId];
}

class RejectAdminEvent extends AuthEvent {
  final String requestId;
  final String reason;

  const RejectAdminEvent({required this.requestId, required this.reason});

  @override
  List<Object?> get props => [requestId, reason];
}

class LoadPendingAdminRequestsEvent extends AuthEvent {}

// States
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitialState extends AuthState {}

class AuthLoadingState extends AuthState {}

class AuthAuthenticatedState extends AuthState {
  final User user;
  final bool isAdmin;
  final bool isSuperAdmin;

  const AuthAuthenticatedState({
    required this.user,
    this.isAdmin = false,
    this.isSuperAdmin = false,
  });

  @override
  List<Object?> get props => [user, isAdmin, isSuperAdmin];
}

class AuthUnauthenticatedState extends AuthState {}

class AuthErrorState extends AuthState {
  final String message;

  const AuthErrorState(this.message);

  @override
  List<Object?> get props => [message];
}

class AdminRegistrationSuccessState extends AuthState {
  final bool isApproved;

  const AdminRegistrationSuccessState({required this.isApproved});

  @override
  List<Object?> get props => [isApproved];
}

class PendingAdminRequestsState extends AuthState {
  final List<Map<String, dynamic>> requests;

  const PendingAdminRequestsState(this.requests);

  @override
  List<Object?> get props => [requests];
}

class AdminRequestActionSuccessState extends AuthState {
  final String action;
  final String requestId;

  const AdminRequestActionSuccessState({
    required this.action,
    required this.requestId,
  });

  @override
  List<Object?> get props => [action, requestId];
}

// Bloc
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  AuthBloc() : super(AuthInitialState()) {
    on<SignInEvent>(_onSignIn);
    on<SignOutEvent>(_onSignOut);
    on<CheckAuthEvent>(_onCheckAuth);
    on<RegisterAdminEvent>(_onRegisterAdmin);
    on<ApproveAdminEvent>(_onApproveAdmin);
    on<RejectAdminEvent>(_onRejectAdmin);
    on<LoadPendingAdminRequestsEvent>(_onLoadPendingAdminRequests);

    // Check authentication status on initialization
    add(CheckAuthEvent());
  }

  Future<void> _onSignIn(SignInEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoadingState());

      // Sign in with Firebase
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );

      final user = userCredential.user;
      if (user == null) {
        emit(const AuthErrorState('User is null after sign in'));
        return;
      }

      // Check if user is an admin
      final userDoc = await _firestore.collection('users').doc(user.uid).get();

      if (!userDoc.exists) {
        await _auth.signOut();
        emit(const AuthErrorState('User account not found'));
        return;
      }

      final userData = userDoc.data()!;
      final isAdmin = userData['isAdmin'] == true;
      final isSuperAdmin = userData['isFirstAdmin'] == true;

      if (!isAdmin) {
        await _auth.signOut();
        emit(const AuthErrorState('You do not have admin privileges'));
        return;
      }

      emit(AuthAuthenticatedState(
        user: user,
        isAdmin: isAdmin,
        isSuperAdmin: isSuperAdmin,
      ));
    } catch (e) {
      debugPrint('AuthBloc: Sign in error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  Future<void> _onSignOut(SignOutEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoadingState());
      await _auth.signOut();
      emit(AuthUnauthenticatedState());
    } catch (e) {
      debugPrint('AuthBloc: Sign out error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  Future<void> _onCheckAuth(CheckAuthEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoadingState());

      final user = _auth.currentUser;

      if (user == null) {
        emit(AuthUnauthenticatedState());
        return;
      }

      // Check if user is an admin
      final userDoc = await _firestore.collection('users').doc(user.uid).get();

      if (!userDoc.exists) {
        await _auth.signOut();
        emit(AuthUnauthenticatedState());
        return;
      }

      final userData = userDoc.data()!;
      final isAdmin = userData['isAdmin'] == true;
      final isSuperAdmin = userData['isFirstAdmin'] == true;

      if (!isAdmin) {
        await _auth.signOut();
        emit(AuthUnauthenticatedState());
        return;
      }

      emit(AuthAuthenticatedState(
        user: user,
        isAdmin: isAdmin,
        isSuperAdmin: isSuperAdmin,
      ));
    } catch (e) {
      debugPrint('AuthBloc: Check auth error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  Future<void> _onRegisterAdmin(RegisterAdminEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoadingState());

      // Create user with Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );

      final user = userCredential.user;
      if (user == null) {
        emit(const AuthErrorState('User is null after creation'));
        return;
      }

      // Check if there are any existing admin accounts
      // We'll check both collections to be thorough
      final adminUsersQuery = await _firestore
          .collection('users')
          .where('isAdmin', isEqualTo: true)
          .get();

      final adminRequestsQuery = await _firestore
          .collection('adminRequests')
          .where('status', isEqualTo: 'approved')
          .get();

      // If both collections are empty, this is the first admin
      final isFirstAdmin = adminUsersQuery.docs.isEmpty && adminRequestsQuery.docs.isEmpty;

      debugPrint('AuthBloc: Admin users count: ${adminUsersQuery.docs.length}');
      debugPrint('AuthBloc: Approved admin requests count: ${adminRequestsQuery.docs.length}');
      debugPrint('AuthBloc: Is first admin: $isFirstAdmin');

      // Set admin status based on whether this is the first admin
      final adminStatus = isFirstAdmin;
      final requestStatus = isFirstAdmin ? 'approved' : 'pending';

      // Create admin request document
      await _firestore.collection('adminRequests').doc(user.uid).set({
        'uid': user.uid,
        'email': event.email,
        'displayName': event.displayName,
        'reason': event.reason,
        'status': requestStatus,
        'requestedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'approvedAt': isFirstAdmin ? FieldValue.serverTimestamp() : null,
        'isFirstAdmin': isFirstAdmin,
      });

      // Create user document with admin status
      await _firestore.collection('users').doc(user.uid).set({
        'uid': user.uid,
        'email': event.email,
        'displayName': event.displayName,
        'role': UserRole.admin.name,
        'isAdmin': adminStatus,
        'isFirstAdmin': isFirstAdmin,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // If this is the first admin, sign in automatically
      if (isFirstAdmin) {
        debugPrint('AuthBloc: First admin account created and automatically approved');
        emit(const AdminRegistrationSuccessState(isApproved: true));

        // Sign in with the new account
        add(SignInEvent(email: event.email, password: event.password));
      } else {
        debugPrint('AuthBloc: Admin request submitted for approval');
        emit(const AdminRegistrationSuccessState(isApproved: false));
      }
    } catch (e) {
      debugPrint('AuthBloc: Register admin error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  Future<void> _onApproveAdmin(ApproveAdminEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoadingState());

      // Get request document
      final requestDoc = await _firestore.collection('adminRequests').doc(event.requestId).get();
      if (!requestDoc.exists) {
        emit(const AuthErrorState('Admin request not found'));
        return;
      }

      final requestData = requestDoc.data()!;
      final userId = requestData['uid'] as String;

      // Update user document to grant admin access
      await _firestore.collection('users').doc(userId).update({
        'isAdmin': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update request status
      await _firestore.collection('adminRequests').doc(event.requestId).update({
        'status': 'approved',
        'approvedAt': FieldValue.serverTimestamp(),
        'approvedBy': _auth.currentUser?.uid,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      emit(AdminRequestActionSuccessState(
        action: 'approved',
        requestId: event.requestId,
      ));

      // Reload pending requests
      add(LoadPendingAdminRequestsEvent());
    } catch (e) {
      debugPrint('AuthBloc: Approve admin error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  Future<void> _onRejectAdmin(RejectAdminEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoadingState());

      // Update request status
      await _firestore.collection('adminRequests').doc(event.requestId).update({
        'status': 'rejected',
        'rejectionReason': event.reason,
        'rejectedAt': FieldValue.serverTimestamp(),
        'rejectedBy': _auth.currentUser?.uid,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      emit(AdminRequestActionSuccessState(
        action: 'rejected',
        requestId: event.requestId,
      ));

      // Reload pending requests
      add(LoadPendingAdminRequestsEvent());
    } catch (e) {
      debugPrint('AuthBloc: Reject admin error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  Future<void> _onLoadPendingAdminRequests(
      LoadPendingAdminRequestsEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoadingState());

      final snapshot = await _firestore
          .collection('adminRequests')
          .where('status', isEqualTo: 'pending')
          .get();

      final requests = snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      emit(PendingAdminRequestsState(requests));
    } catch (e) {
      debugPrint('AuthBloc: Load pending admin requests error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }
}
