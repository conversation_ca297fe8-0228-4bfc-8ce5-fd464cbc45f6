import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/login_request.dart';
import '../models/register_request.dart';
import '../../../models/user/user_model.dart';
import '../../../services/auth/supabase_auth_service.dart';

/// Supabase implementation of AuthRepository
class SupabaseAuthRepository {
  final SupabaseAuthService _authService = SupabaseAuthService.instance;
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get auth state changes stream
  Stream<UserModel?> get authStateChanges {
    return _supabase.auth.onAuthStateChange.asyncMap((state) async {
      if (state.session?.user == null) return null;
      
      // Get user profile from database
      final userProfile = await _getUserProfile(state.session!.user.id);
      return userProfile ?? UserModel.fromSupabaseUser(state.session!.user);
    });
  }

  /// Get current user
  UserModel? get currentUser {
    return _authService.currentUserModel;
  }

  /// Get current user async
  Future<UserModel?> getCurrentUser() async {
    final user = _authService.currentUser;
    if (user == null) return null;
    
    final userProfile = await _getUserProfile(user.id);
    return userProfile ?? UserModel.fromSupabaseUser(user);
  }

  /// Sign in with email and password
  Future<UserModel> signInWithEmailAndPassword(LoginRequest request) async {
    try {
      return await _authService.signInWithEmailAndPassword(request);
    } catch (e) {
      debugPrint('SupabaseAuthRepository: Sign in error: $e');
      rethrow;
    }
  }

  /// Sign in with Google
  Future<UserModel> signInWithGoogle() async {
    try {
      return await _authService.signInWithGoogle();
    } catch (e) {
      debugPrint('SupabaseAuthRepository: Google sign in error: $e');
      rethrow;
    }
  }

  /// Sign in with Apple (placeholder - implement if needed)
  Future<UserModel> signInWithApple() async {
    throw UnimplementedError('Apple sign in not implemented for Supabase');
  }

  /// Register new user
  Future<UserModel> register(RegisterRequest request) async {
    try {
      return await _authService.register(request);
    } catch (e) {
      debugPrint('SupabaseAuthRepository: Registration error: $e');
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut({bool clearAllCredentials = false}) async {
    try {
      await _authService.signOut();
    } catch (e) {
      debugPrint('SupabaseAuthRepository: Sign out error: $e');
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _authService.resetPassword(email);
    } catch (e) {
      debugPrint('SupabaseAuthRepository: Reset password error: $e');
      rethrow;
    }
  }

  /// Update profile
  Future<UserModel> updateProfile({
    String? displayName,
    String? photoUrl,
    String? phoneNumber,
  }) async {
    try {
      return await _authService.updateProfile(
        displayName: displayName,
        photoUrl: photoUrl,
        phoneNumber: phoneNumber,
      );
    } catch (e) {
      debugPrint('SupabaseAuthRepository: Update profile error: $e');
      rethrow;
    }
  }

  /// Delete account
  Future<void> deleteAccount() async {
    try {
      await _authService.deleteAccount();
    } catch (e) {
      debugPrint('SupabaseAuthRepository: Delete account error: $e');
      rethrow;
    }
  }

  /// Verify phone number (placeholder - implement if needed)
  Future<void> verifyPhone(String phoneNumber) async {
    throw UnimplementedError('Phone verification not implemented for Supabase');
  }

  /// Get user profile from database
  Future<UserModel?> _getUserProfile(String userId) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select()
          .eq('id', userId)
          .eq('is_deleted', false)
          .maybeSingle();
      
      if (response == null) return null;
      
      return UserModel.fromSupabaseJson(response);
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      return null;
    }
  }

  /// Create or update user in database
  Future<UserModel> _createOrUpdateUser(
    User user, {
    UserRole? role,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final existingProfile = await _getUserProfile(user.id);
      
      if (existingProfile != null) {
        // Update existing profile
        final updates = {
          'email': user.email,
          'display_name': user.userMetadata?['display_name'] ?? user.email?.split('@')[0],
          'photo_url': user.userMetadata?['avatar_url'],
          'phone_number': user.phone,
          'email_verified': user.emailConfirmedAt != null,
          'updated_at': DateTime.now().toIso8601String(),
          ...?metadata,
        };
        
        await _supabase.from('user_profiles').update(updates).eq('id', user.id);
        
        return existingProfile.copyWith(
          email: user.email ?? existingProfile.email,
          displayName: updates['display_name'] ?? existingProfile.displayName,
          photoUrl: updates['photo_url'] ?? existingProfile.photoUrl,
          phoneNumber: updates['phone_number'] ?? existingProfile.phoneNumber,
          emailVerified: updates['email_verified'] ?? existingProfile.emailVerified,
          updatedAt: DateTime.now(),
        );
      } else {
        // Create new profile
        final profileData = {
          'id': user.id,
          'email': user.email!,
          'display_name': user.userMetadata?['display_name'] ?? user.email!.split('@')[0],
          'photo_url': user.userMetadata?['avatar_url'],
          'phone_number': user.phone,
          'role': role?.name ?? 'buyer',
          'email_verified': user.emailConfirmedAt != null,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
          'is_deleted': false,
          ...?metadata,
        };
        
        await _supabase.from('user_profiles').insert(profileData);
        
        return UserModel.fromSupabaseJson(profileData);
      }
    } catch (e) {
      debugPrint('Error creating/updating user: $e');
      rethrow;
    }
  }

  /// Handle authentication exceptions
  Exception _handleAuthException(dynamic error) {
    if (error is AuthException) {
      switch (error.statusCode) {
        case '400':
          return Exception('Invalid email or password');
        case '422':
          return Exception('Email already registered');
        case '429':
          return Exception('Too many requests. Please try again later');
        default:
          return Exception(error.message);
      }
    }
    return Exception('Authentication failed: $error');
  }
}

/// Extension to add Supabase support to UserModel
extension UserModelSupabaseExtension on UserModel {
  /// Create UserModel from Supabase User
  static UserModel fromSupabaseUser(User user) {
    return UserModel(
      id: user.id,
      email: user.email ?? '',
      displayName: user.userMetadata?['display_name'] ?? user.email?.split('@')[0] ?? '',
      photoUrl: user.userMetadata?['avatar_url'],
      phoneNumber: user.phone,
      emailVerified: user.emailConfirmedAt != null,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt ?? user.createdAt,
    );
  }

  /// Create UserModel from Supabase JSON
  static UserModel fromSupabaseJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      displayName: json['display_name'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      photoUrl: json['photo_url'],
      phoneNumber: json['phone_number'],
      emailVerified: json['email_verified'] ?? false,
      role: UserRole.values.firstWhere(
        (role) => role.name == json['role'],
        orElse: () => UserRole.buyer,
      ),
      status: UserStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => UserStatus.active,
      ),
      verificationStatus: VerificationStatus.values.firstWhere(
        (status) => status.name == json['verification_status'],
        orElse: () => VerificationStatus.unverified,
      ),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      isDeleted: json['is_deleted'] ?? false,
    );
  }
}
