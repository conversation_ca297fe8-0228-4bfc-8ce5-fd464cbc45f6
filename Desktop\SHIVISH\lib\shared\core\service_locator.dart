import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/auth/auth_service.dart';
import '../services/firestore_service.dart';
import '../services/storage/storage_service.dart';
import '../services/analytics/analytics_service.dart';
import '../services/messaging_service.dart';
import '../core/auth/repositories/auth_repository.dart';
import '../services/product/product_service.dart';
import '../services/review/review_service.dart';
import '../services/executor_service.dart';
import '../services/seller_service.dart';
import '../services/priest_service.dart';
import '../services/technician_service.dart';
import '../../apps/admin/bloc/executor/executor_bloc.dart';
import '../../apps/admin/bloc/priest/priest_bloc.dart';
import '../../apps/admin/bloc/product/product_bloc.dart';
import '../../apps/admin/bloc/seller/seller_bloc.dart';
import '../../apps/admin/bloc/technician/technician_bloc.dart';
import 'package:injectable/injectable.dart';
import '../../apps/admin/bloc/analytics/analytics_bloc.dart';
import '../services/commission/commission_service.dart';
import '../../apps/admin/bloc/commission/commission_bloc.dart';
import '../services/payment_gateway/payment_gateway_service.dart';
import '../services/payment_gateway/payment_gateway_bloc.dart';
import '../services/notification_service.dart';
import '../services/mock_notification_service.dart';
import '../services/debug_service.dart';
import 'package:shivish/shared/services/language_service.dart';
import '../services/payment/biometric_auth_service.dart';
import '../services/backup/hybrid_backup_service.dart';
import '../services/backup/backup_scheduler.dart';
import '../services/backup/backup_config_manager.dart';
import '../services/backup/backup_restore_service.dart';
import '../services/backup/backup_encryption_service.dart';
import '../services/backup_service.dart';
import '../services/location/location_service.dart';
import '../services/system_config_service.dart';
import '../repositories/system_config_repository.dart';
import '../services/tax/tax_update_service.dart';
import '../services/storage/adaptive_storage_service.dart';
import '../services/storage/storage_config_listener.dart';

final GetIt serviceLocator = GetIt.instance;

// Global navigator key for accessing context from anywhere
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

@InjectableInit()
Future<void> setupServiceLocator() async {
  // Register Supabase services as singletons
  if (!serviceLocator.isRegistered<SupabaseClient>()) {
    serviceLocator
        .registerLazySingleton<SupabaseClient>(() => Supabase.instance.client);
  }
  if (!serviceLocator.isRegistered<GoogleSignIn>()) {
    serviceLocator.registerLazySingleton<GoogleSignIn>(() => GoogleSignIn());
  }

  // Register a mock NotificationService for the HomeScreen
  if (!serviceLocator.isRegistered<NotificationService>()) {
    serviceLocator.registerLazySingleton<NotificationService>(() {
      // Create a mock implementation that doesn't require a GoRouter
      return MockNotificationService();
    });
    debugPrint('Mock NotificationService registered successfully');
  }

  // Register DebugService
  if (!serviceLocator.isRegistered<DebugService>()) {
    serviceLocator.registerLazySingleton<DebugService>(() => DebugService());
  }

  // Register SharedPreferences
  final prefs = await SharedPreferences.getInstance();
  if (!serviceLocator.isRegistered<SharedPreferences>()) {
    serviceLocator.registerSingleton<SharedPreferences>(prefs);
  }

  // Register language service
  final languageService = await LanguageService.initialize();
  if (!serviceLocator.isRegistered<LanguageService>()) {
    serviceLocator.registerSingleton<LanguageService>(languageService);
  }

  // Register services as singletons
  if (!serviceLocator.isRegistered<AuthService>()) {
    serviceLocator.registerLazySingleton<AuthService>(() => AuthService());
  }
  if (!serviceLocator.isRegistered<FirestoreService>()) {
    serviceLocator
        .registerLazySingleton<FirestoreService>(() => FirestoreService());
  }
  // Register adaptive storage service
  if (!serviceLocator.isRegistered<AdaptiveStorageService>()) {
    final adaptiveStorageService = AdaptiveStorageService();
    // Initialize the service
    adaptiveStorageService.initialize();
    serviceLocator.registerSingleton<AdaptiveStorageService>(adaptiveStorageService);
  }

  // Register storage config listener
  if (!serviceLocator.isRegistered<StorageConfigListener>()) {
    final storageConfigListener = StorageConfigListener(null);
    storageConfigListener.initialize();
    serviceLocator.registerSingleton<StorageConfigListener>(storageConfigListener);
  }
  if (!serviceLocator.isRegistered<AnalyticsService>()) {
    serviceLocator.registerSingleton<AnalyticsService>(AnalyticsService());
  }
  if (!serviceLocator.isRegistered<MessagingService>()) {
    serviceLocator
        .registerLazySingleton<MessagingService>(() => MessagingService());
  }

  if (!serviceLocator.isRegistered<AuthRepository>()) {
    serviceLocator.registerSingleton<AuthRepository>(AuthRepository());
  }
  if (!serviceLocator.isRegistered<ProductService>()) {
    serviceLocator
        .registerLazySingleton<ProductService>(() => ProductService());
  }
  if (!serviceLocator.isRegistered<ReviewService>()) {
    serviceLocator.registerLazySingleton<ReviewService>(
      () => ReviewService(),
    );
  }

  // Register ExecutorService and ExecutorBloc
  if (!serviceLocator.isRegistered<ExecutorService>()) {
    serviceLocator.registerLazySingleton<ExecutorService>(
      () => ExecutorService(
        serviceLocator<SupabaseClient>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }
  if (!serviceLocator.isRegistered<ExecutorBloc>()) {
    serviceLocator.registerFactory<ExecutorBloc>(
      () => ExecutorBloc(serviceLocator<ExecutorService>()),
    );
  }

  // Register PriestService and PriestBloc
  if (!serviceLocator.isRegistered<PriestService>()) {
    serviceLocator.registerSingleton<PriestService>(
      PriestService(
        serviceLocator<SupabaseClient>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }
  if (!serviceLocator.isRegistered<PriestBloc>()) {
    serviceLocator.registerFactory<PriestBloc>(
      () => PriestBloc(serviceLocator<PriestService>()),
    );
  }

  // Register ProductBloc
  if (!serviceLocator.isRegistered<ProductBloc>()) {
    serviceLocator.registerFactory<ProductBloc>(
      () => ProductBloc(serviceLocator<ProductService>()),
    );
  }

  // Register SellerService
  if (!serviceLocator.isRegistered<SellerService>()) {
    serviceLocator.registerLazySingleton<SellerService>(
      () => SellerService(
        serviceLocator<SupabaseClient>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }

  // Register SellerBloc
  if (!serviceLocator.isRegistered<SellerBloc>()) {
    serviceLocator.registerFactory<SellerBloc>(
      () => SellerBloc(serviceLocator<SellerService>()),
    );
  }

  // Register TechnicianService if not already registered
  if (!serviceLocator.isRegistered<TechnicianService>()) {
    serviceLocator.registerLazySingleton<TechnicianService>(
      () => TechnicianService(
        serviceLocator<SupabaseClient>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }

  // Register TechnicianBloc
  if (!serviceLocator.isRegistered<TechnicianBloc>()) {
    serviceLocator.registerFactory<TechnicianBloc>(
      () => TechnicianBloc(serviceLocator<TechnicianService>()),
    );
  }

  // Register AnalyticsBloc
  if (!serviceLocator.isRegistered<AnalyticsBloc>()) {
    serviceLocator.registerFactory<AnalyticsBloc>(
      () => AnalyticsBloc(serviceLocator<AnalyticsService>()),
    );
  }

  // Register backup services
  if (!serviceLocator.isRegistered<BackupService>()) {
    serviceLocator.registerLazySingleton<BackupService>(
      () => BackupService(serviceLocator<SupabaseClient>()),
    );
  }

  if (!serviceLocator.isRegistered<BackupEncryptionService>()) {
    serviceLocator.registerLazySingleton<BackupEncryptionService>(
      () => BackupEncryptionService(),
    );
  }

  if (!serviceLocator.isRegistered<HybridBackupService>()) {
    serviceLocator.registerLazySingleton<HybridBackupService>(
      () => HybridBackupService(
        encryptionService: serviceLocator<BackupEncryptionService>(),
      ),
    );
  }

  if (!serviceLocator.isRegistered<BackupRestoreService>()) {
    serviceLocator.registerLazySingleton<BackupRestoreService>(
      () => BackupRestoreService(
        encryptionService: serviceLocator<BackupEncryptionService>(),
      ),
    );
  }

  if (!serviceLocator.isRegistered<BackupScheduler>()) {
    serviceLocator.registerLazySingleton<BackupScheduler>(
      () => BackupScheduler(serviceLocator<HybridBackupService>()),
    );
  }

  if (!serviceLocator.isRegistered<BackupConfigManager>()) {
    serviceLocator.registerLazySingleton<BackupConfigManager>(
      () => BackupConfigManager(),
    );
  }

  // Register LocationService
  if (!serviceLocator.isRegistered<LocationService>()) {
    serviceLocator.registerLazySingleton<LocationService>(
      () => LocationService(),
    );
  }

  // Register SystemConfigService
  if (!serviceLocator.isRegistered<SystemConfigService>()) {
    serviceLocator.registerLazySingleton<SystemConfigService>(() {
      final repository = SystemConfigRepository(serviceLocator<SupabaseClient>());
      return SystemConfigService(repository);
    });
  }

  // Register PaymentGatewayService
  if (!serviceLocator.isRegistered<PaymentGatewayService>()) {
    serviceLocator.registerLazySingleton<PaymentGatewayService>(() {
      return PaymentGatewayService(serviceLocator<SupabaseClient>());
    });
  }

  // BackupController registration is temporarily commented out
  // until the controller is properly implemented
  /*
  if (!serviceLocator.isRegistered<BackupController>()) {
    serviceLocator.registerLazySingleton<BackupController>(
      () => BackupController(
        backupService: serviceLocator<HybridBackupService>(),
        scheduler: serviceLocator<BackupScheduler>(),
        configManager: serviceLocator<BackupConfigManager>(),
        restoreService: serviceLocator<BackupRestoreService>(),
      ),
    );
  }
  */

  // Initialize services that require async initialization
  try {
    final messagingService = serviceLocator<MessagingService>();
    await messagingService.initialize();
    debugPrint('MessagingService initialized successfully');
  } catch (e) {
    debugPrint('MessagingService initialization failed: $e');
    // Continue without messaging service
  }

  // Initialize biometric authentication service
  try {
    debugPrint('Initializing BiometricAuthService...');
    await BiometricAuthService.initialize();
    debugPrint('BiometricAuthService initialized');
  } catch (e) {
    debugPrint('BiometricAuthService initialization failed: $e');
    // Continue without biometric authentication service
  }

  // Initialize location service
  try {
    debugPrint('Initializing LocationService...');
    final locationService = serviceLocator<LocationService>();
    await locationService.initialize();
    debugPrint('LocationService initialized');
  } catch (e) {
    debugPrint('LocationService initialization failed: $e');
    // Continue without location service
  }

  // Initialize tax update service
  try {
    debugPrint('Initializing TaxUpdateService...');
    final taxUpdateService = TaxUpdateService();
    await taxUpdateService.initialize();
    taxUpdateService.start();
    serviceLocator.registerSingleton<TaxUpdateService>(taxUpdateService);
    debugPrint('TaxUpdateService initialized and started');
  } catch (e) {
    debugPrint('TaxUpdateService initialization failed: $e');
    // Continue without tax update service
  }
}

// Extension method for easier access to services
extension ServiceLocatorExtension on GetIt {
  AuthService get auth => get<AuthService>();
  FirestoreService get firestore => get<FirestoreService>();
  StorageService get storage => get<StorageService>();
  AnalyticsService get analytics => get<AnalyticsService>();
  MessagingService get messaging => get<MessagingService>();
  AuthRepository get authRepository => get<AuthRepository>();
  ProductService get product => get<ProductService>();
  ReviewService get review => get<ReviewService>();
  ExecutorService get executor => get<ExecutorService>();
  ExecutorBloc get executorBloc => get<ExecutorBloc>();
  PriestService get priest => get<PriestService>();
  PriestBloc get priestBloc => get<PriestBloc>();
  SellerService get seller => get<SellerService>();
  SellerBloc get sellerBloc => get<SellerBloc>();
  TechnicianService get technician => get<TechnicianService>();
  TechnicianBloc get technicianBloc => get<TechnicianBloc>();
  AnalyticsBloc get analyticsBloc => get<AnalyticsBloc>();
  DebugService get debug => get<DebugService>();
  LocationService get location => get<LocationService>();
  SystemConfigService get systemConfig => get<SystemConfigService>();
  PaymentGatewayService get paymentGateway => get<PaymentGatewayService>();
  BackupService get backup => get<BackupService>();
}

// Analytics - Temporarily commenting out until updated for Supabase
/*
// @module
abstract class AnalyticsModule {
  // @singleton
  AnalyticsService get analyticsService => AnalyticsService();

  // @singleton
  AnalyticsBloc get analyticsBloc => AnalyticsBloc(analyticsService);
}
*/



// Temporarily commenting out to fix duplicate registration
// @module
abstract class RegisterModule {
  // @injectable
  CommissionService commissionService(SupabaseClient supabase) =>
      CommissionService(supabase);

  // @injectable
  CommissionBloc commissionBloc(CommissionService service) =>
      CommissionBloc(service);

  @injectable
  PaymentGatewayService paymentGatewayService(SupabaseClient supabase) =>
      PaymentGatewayService(supabase);

  @injectable
  PaymentGatewayBloc paymentGatewayBloc(PaymentGatewayService service) =>
      PaymentGatewayBloc(service);
}
