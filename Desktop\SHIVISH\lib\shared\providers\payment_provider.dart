import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/payment/payment_model.dart' as model;
import '../models/payment/payment_method.dart';
import '../utils/logger.dart';
import 'auth_provider.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../services/payment/payment_service_provider.dart' as provider;
import 'package:supabase_flutter/supabase_flutter.dart';

part 'payment_provider.freezed.dart';

// Use the providers from payment_service_provider.dart

/// Stream provider for user's payment history
final userPaymentHistoryProvider =
    StreamProvider.family<List<model.PaymentModel>, String>((ref, userId) {
  final paymentService = ref.watch(provider.paymentServiceProvider);
  return paymentService.getPaymentsByUser(userId);
});

/// Stream provider for payments requiring attention
final paymentsRequiringAttentionProvider =
    StreamProvider<List<model.PaymentModel>>((ref) {
  final paymentService = ref.watch(provider.paymentServiceProvider);
  return paymentService.getPaymentsRequiringAttention();
});

@freezed
class PaymentState with _$PaymentState {
  const factory PaymentState({
    @Default([]) List<PaymentMethod> savedPaymentMethods,
    @Default(false) bool isLoading,
    String? error,
    String? paymentId,
    String? paymentStatus,
  }) = _PaymentState;
}

final paymentProvider =
    StateNotifierProvider<PaymentNotifier, PaymentState>((ref) {
  return PaymentNotifier(ref);
});

class PaymentNotifier extends StateNotifier<PaymentState> {
  final _logger = getLogger('PaymentNotifier');
  final Ref _ref;

  PaymentNotifier(this._ref) : super(const PaymentState());

  Future<void> loadSavedPaymentMethods() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final paymentService = _ref.read(provider.paymentServiceProvider);
      final cards = await paymentService.listenToUserCards().first;

      final paymentMethods = cards
          .map((card) => PaymentMethod(
                id: card.id,
                type: PaymentMethodType.card,
                title: '${card.brand} Card',
                subtitle: '**** ${card.last4}',
                cardNumber: card.last4,
                cardBrand: card.brand,
                expiryMonth: card.expiryMonth.toString(),
                expiryYear: card.expiryYear.toString(),
              ))
          .toList();

      state = state.copyWith(
        isLoading: false,
        savedPaymentMethods: paymentMethods,
      );
    } catch (e, st) {
      _logger.severe('Error loading saved payment methods', e, st);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load saved payment methods: ${e.toString()}',
      );
    }
  }

  Future<void> initiatePayment({
    required String orderId,
    required double amount,
    required PaymentMethod method,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Use UPI service for payments
      final upiService = _ref.read(provider.upiServiceProvider);
      final userAsync = _ref.read(currentUserProvider);

      if (userAsync.value == null) {
        throw Exception('User not authenticated');
      }

      final user = userAsync.value!;

      model.PaymentModel payment;
      switch (method.type) {
        case PaymentMethodType.card:
          // Use UPI service for now until PhonePe service is implemented
          final upiPayment = await upiService.startPayment(
            orderId: orderId,
            amount: amount,
            receiverName: 'Shivish',
            description: 'Order Payment',
          );
          if (upiPayment == null) {
            throw Exception('Payment failed');
          }
          payment = model.PaymentModel(
            id: '', // Will be set by PhonePe callback
            paymentNumber: orderId,
            orderId: orderId,
            customerId: user.uid,
            merchantId: '', // Will be set by provider
            type: model.PaymentType.purchase,
            status: model.PaymentStatus.processing,
            method: model.PaymentMethod.card,
            gateway: model.PaymentGateway.phonepe,
            amount: amount,
            taxAmount: 0, // Will be set by provider
            feeAmount: 0, // Will be set by provider
            totalAmount: amount,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          break;
        case PaymentMethodType.upi:
          final upiPayment = await upiService.startPayment(
            orderId: orderId,
            amount: amount,
            receiverName: 'Shivish',
            description: 'Order Payment',
          );
          if (upiPayment == null) {
            throw Exception('UPI payment failed');
          }
          payment = upiPayment;
          break;
        case PaymentMethodType.netbanking:
          payment = model.PaymentModel(
            id: '', // Will be set by provider
            paymentNumber: orderId,
            orderId: orderId,
            customerId: user.uid,
            merchantId: '', // Will be set by provider
            type: model.PaymentType.purchase,
            status: model.PaymentStatus.processing,
            method: model.PaymentMethod.upi,
            gateway: model.PaymentGateway.upi,
            amount: amount,
            taxAmount: 0, // Will be set by provider
            feeAmount: 0, // Will be set by provider
            totalAmount: amount,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          break;
      }

      state = state.copyWith(
        isLoading: false,
        paymentId: payment.id,
        paymentStatus: payment.status.name,
      );
    } catch (e, st) {
      _logger.severe('Error initiating payment', e, st);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initiate payment: ${e.toString()}',
      );
    }
  }
}
