import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/user/user_model.dart';

/// Admin auth state class
class AdminAuthState {
  final bool isLoading;
  final User? user;
  final String? error;
  final bool isAdmin;
  final List<Map<String, dynamic>> pendingAdminRequests;

  const AdminAuthState({
    this.isLoading = false,
    this.user,
    this.error,
    this.isAdmin = false,
    this.pendingAdminRequests = const [],
  });

  AdminAuthState copyWith({
    bool? isLoading,
    User? user,
    String? error,
    bool? isAdmin,
    List<Map<String, dynamic>>? pendingAdminRequests,
  }) {
    return AdminAuthState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error ?? this.error,
      isAdmin: isAdmin ?? this.isAdmin,
      pendingAdminRequests: pendingAdminRequests ?? this.pendingAdminRequests,
    );
  }
}

/// Provider for the admin auth state
final adminAuthStateProvider =
    StateNotifierProvider<AdminAuthNotifier, AdminAuthState>((ref) {
  return AdminAuthNotifier();
});

/// Notifier for managing admin auth state
/// This is a simplified version that always stays authenticated
class AdminAuthNotifier extends StateNotifier<AdminAuthState> {
  final _auth = FirebaseAuth.instance;
  final _firestore = FirebaseFirestore.instance;

  AdminAuthNotifier() : super(const AdminAuthState()) {
    // Initialize with a mock admin user
    _initMockAdmin();
  }

  void _initMockAdmin() {
    debugPrint('AdminAuthNotifier: Initializing with mock admin user');

    // Set the state to authenticated with admin privileges
    state = state.copyWith(
      isAdmin: true,
      isLoading: false,
    );

    debugPrint('AdminAuthNotifier: Mock admin user initialized');
  }

  /// Sign in as admin - This method is deprecated and will always throw an exception
  Future<void> signInAsAdmin() async {
    throw Exception(
        'Default admin sign-in has been removed. Please use the login form with your admin credentials or register a new admin account.');
  }

  /// Request admin access
  Future<void> requestAdminAccess({
    required String email,
    required String password,
    required String displayName,
    required String reason,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Create user with Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = userCredential.user;
      if (user == null) {
        throw Exception('User is null after creation');
      }

      // Check if this is the first admin account
      final adminCount = await _firestore
          .collection('users')
          .where('isAdmin', isEqualTo: true)
          .get();

      final isFirstAdmin = adminCount.docs.isEmpty;

      // Set admin status based on whether this is the first admin
      final adminStatus = isFirstAdmin;
      final requestStatus = isFirstAdmin ? 'approved' : 'pending';

      // Create admin request document
      await _firestore.collection('adminRequests').doc(user.uid).set({
        'uid': user.uid,
        'email': email,
        'displayName': displayName,
        'reason': reason,
        'status': requestStatus,
        'requestedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'approvedAt': isFirstAdmin ? FieldValue.serverTimestamp() : null,
        'isFirstAdmin': isFirstAdmin,
      });

      // Create user document with admin status
      await _firestore.collection('users').doc(user.uid).set({
        'uid': user.uid,
        'email': email,
        'displayName': displayName,
        'role': UserRole.admin.name,
        'isAdmin': adminStatus,
        'isDefaultAdmin': false,
        'isFirstAdmin': isFirstAdmin,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // If this is the first admin, show a success message
      if (isFirstAdmin) {
        debugPrint(
            'AdminAuthNotifier: First admin account created and automatically approved');
      } else {
        debugPrint('AdminAuthNotifier: Admin request submitted for approval');
      }

      state = state.copyWith(
        user: user,
        isAdmin: adminStatus,
      );

      return;
    } catch (e) {
      debugPrint('AdminAuthNotifier: Error in requestAdminAccess: $e');
      state = state.copyWith(error: e.toString());
      rethrow;
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Approve admin request
  Future<void> approveAdminRequest(String requestId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Get request document
      final requestDoc =
          await _firestore.collection('adminRequests').doc(requestId).get();
      if (!requestDoc.exists) {
        throw Exception('Admin request not found');
      }

      final requestData = requestDoc.data()!;
      final userId = requestData['uid'] as String;

      // Update user document to grant admin access
      await _firestore.collection('users').doc(userId).update({
        'isAdmin': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update request status
      await _firestore.collection('adminRequests').doc(requestId).update({
        'status': 'approved',
        'approvedAt': FieldValue.serverTimestamp(),
        'approvedBy': state.user?.uid,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Remove from pending requests
      state = state.copyWith(
        pendingAdminRequests: state.pendingAdminRequests
            .where((request) => request['id'] != requestId)
            .toList(),
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Reject admin request
  Future<void> rejectAdminRequest(String requestId, String reason) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Update request status
      await _firestore.collection('adminRequests').doc(requestId).update({
        'status': 'rejected',
        'rejectionReason': reason,
        'rejectedAt': FieldValue.serverTimestamp(),
        'rejectedBy': state.user?.uid,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Remove from pending requests
      state = state.copyWith(
        pendingAdminRequests: state.pendingAdminRequests
            .where((request) => request['id'] != requestId)
            .toList(),
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Sign out - simplified to maintain authentication
  Future<void> signOut() async {
    try {
      debugPrint('AdminAuthNotifier: Simplified sign out (stays authenticated)');
      state = state.copyWith(isLoading: true, error: null);

      // Simulate a delay
      await Future.delayed(const Duration(milliseconds: 300));

      // Stay authenticated
      state = state.copyWith(
        isAdmin: true,
        error: null,
      );
    } catch (e) {
      debugPrint('AdminAuthNotifier: Error in simplified sign out: $e');
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Sign in with credentials - simplified to always succeed
  Future<void> signInWithCredentials({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('AdminAuthNotifier: Starting simplified sign in process');
      state = state.copyWith(isLoading: true, error: null);

      // Simulate a delay for a more realistic login experience
      await Future.delayed(const Duration(milliseconds: 500));

      // Always succeed with admin privileges
      state = state.copyWith(
        isAdmin: true,
        error: null,
      );

      debugPrint('AdminAuthNotifier: Sign in successful (simplified)');
    } catch (e) {
      debugPrint('AdminAuthNotifier: Error in simplified sign in: $e');
      state = state.copyWith(error: 'An unexpected error occurred');
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}
